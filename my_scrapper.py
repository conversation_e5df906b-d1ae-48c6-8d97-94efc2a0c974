import requests
from bs4 import BeautifulSoup

# Send an HTTP request to the URL
url = "https://rawstory.com"
response = requests.get(url)

# Check if the request was successful
if response.status_code != 200:
    print("Failed to retrieve page")
else:
    # Parse the HTML content using Beautiful Soup
    soup = BeautifulSoup(response.content, 'html.parser')

    # Find all links on the webpage
    links = soup.find_all('a')

    # Print each link's text and URL
    for link in links:
        print(f"{link.get_text()}: {link.get('href')}")