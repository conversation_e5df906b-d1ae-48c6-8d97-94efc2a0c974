[{"name": "get_weather", "description": "Retrieves the current weather for a given city.", "parameters": [{"name": "city", "type": "string", "description": "The city to get the weather for."}]}, {"name": "search_wikipedia", "description": "Searches Wikipedia for a given query.", "parameters": [{"name": "query", "type": "string", "description": "The search query."}]}, {"name": "scrape_news", "description": "Scrapes the latest news headlines from Raw Story or other news websites.", "parameters": [{"name": "url", "type": "string", "description": "The news website URL to scrape (optional, defaults to rawstory.com)", "required": false}]}]